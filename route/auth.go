package route

import (
	"events-api/database"
	"events-api/model"
	"github.com/gin-gonic/gin"
)

func Register(c *gin.Context) {
	var user model.User
	if err := c.ShouldBindJSON(&user); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}
	// Hash the password before storing (e.g., using bcrypt)
	database.DB.Create(&user)
	c.<PERSON>(201, gin.H{"message": "User registered"})
}

func Login(c *gin.Context) {
	// Check user credentials, return JWT
}

func GetProfile(c *gin.Context) {
	// Protected endpoint
	c.J<PERSON>(200, gin.H{"message": "Success"})
}
