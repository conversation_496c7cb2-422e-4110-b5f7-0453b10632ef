package model

import "github.com/gin-gonic/gin"

type Login struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

func loginHandler(c *gin.Context) {
	var json Login

	if err := c.Should<PERSON>ind<PERSON>(&json); err != nil {
		c.J<PERSON>(400, gin.H{"error": err.<PERSON><PERSON><PERSON>()})
		return
	}
	c.<PERSON>(200, gin.H{"status": "success"})
}
