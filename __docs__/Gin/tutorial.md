The **Gin framework** is a popular choice in Go for building high-performance web applications and REST APIs. 
It supports routing, middleware, validation, error handling, and integrates easily with other libraries like GORM for database access.

Below you'll find a comprehensive overview of Gin's key aspects, example code, and a complete tutorial for a REST API with authentication, authorization, middleware, GORM integration, and Docker containerization.

## Key Topics of Gin Framework

### Routing
Gin's routing is fast and intuitive, organizing endpoints with clear route registration:

```go
import "github.com/gin-gonic/gin"

func main() {
    r := gin.Default()
    r.GET("/ping", func(c *gin.Context) {
        c.JSON(200, gin.H{"message": "pong"})
    })
    r.Run() // By default, serves on :8080
}
```
This example demonstrates how Gin handles simple GET requests1. 2. .

### Middleware

Middleware in Gin lets you add logic (logging, authentication, etc.) that wraps around handlers:

```go
func LoggerMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        log.Println("Request received")
        c.Next()
        log.Println("Request completed")
    }
}

// Usage as a global middleware
router := gin.Default()

router.Use(LoggerMiddleware())
```

Middleware can abort requests or control the flow, often used for authentication or custom logging3. 1. .

### Request & Response Handling

Gin simplifies parsing JSON and sending data:

```go
type Login struct {
    Username string `json:"username" binding:"required"`
    Password string `json:"password" binding:"required"`
}

func loginHandler(c *gin.Context) {
    var json Login

    if err := c.ShouldBindJSON(&json); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    c.JSON(200, gin.H{"status": "success"})
}
```

Gin's binding and validation features reduce boilerplate for input validation1. 4. .

### Grouping Routes

Grouping helps organize endpoints:

```go
v1 := r.Group("/api/v1")
{
    v1.GET("/users", getUsers)
    v1.POST("/users", createUser)
}
```
This is useful for versioning and structuring large APIs1. .

### Error Handling
Errors can be captured and responded to in a standardized way:

```go
r.GET("/fail", func(c *gin.Context) {
    c.JSON(400, gin.H{"error": "Something failed"})
})
```
Advanced error management is possible using Gin’s context object5. .

-------------

### Integration with GORM

GORM is the leading ORM in Go. 
Gin easily works with it for database-powered APIs:

```go
import (
    "github.com/jinzhu/gorm"
    _ "github.com/jinzhu/gorm/dialects/postgres"
)

db, err := gorm.Open("postgres", "host=localhost user=... dbname=...")
if err != nil {
    panic("failed to connect to database")
}

// Auto-migrate your model
db.AutoMigrate(&User{})
```

Endpoints then use this DB handle to persist and query data6. 7. 8. .

## Complete Tutorial: REST API with Gin, AUTH, GORM, Middleware, and Docker

### 1. Initialize a Project

```sh
go mod init ginapp

go get -u github.com/gin-gonic/gin
go get -u github.com/jinzhu/gorm
go get -u github.com/jinzhu/gorm/dialects/postgres
go get -u github.com/dgrijalva/jwt-go

```

### 2. Define the User Model

```go
type User struct {
    gorm.Model
    Username string `json:"username" gorm:"unique"`
    Password string `json:"password"`
}
```


### 3. Setup GORM and Gin

```go
var db *gorm.DB

func init() {
    var err error
    db, err = gorm.Open("postgres", "host=localhost user=postgres password=postgres dbname=ginapp sslmode=disable")
    if err != nil {
        panic(err)
    }
    db.AutoMigrate(&User{})
}
```


### 4. Middleware for JWT Authentication

```go
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tokenString := c.GetHeader("Authorization")
        if tokenString == "" || !validateToken(tokenString) {
            c.JSON(401, gin.H{"error": "Authentication required"})
            c.Abort()
            return
        }
        c.Next()
    }
}

func validateToken(token string) bool {
    // JWT validation logic here
    return true
}
```


### 5. REST Endpoints

```go
func Register(c *gin.Context) {
    var user User
    if err := c.ShouldBindJSON(&user); err != nil {
        c.JSON(400, gin.H{"error": "Invalid input"})
        return
    }
    // Hash the password before storing (e.g., using bcrypt)
    db.Create(&user)
    c.JSON(201, gin.H{"message": "User registered"})
}

func Login(c *gin.Context) {
    // Check user credentials, return JWT
}

func GetProfile(c *gin.Context) {
    // Protected endpoint
    c.JSON(200, gin.H{"message": "Success"})
}
```


### 6. Apply Middleware

```go
r := gin.Default()
r.POST("/register", Register)
r.POST("/login", Login)

authorized := r.Group("/")
authorized.Use(AuthMiddleware())
authorized.GET("/profile", GetProfile)
```


### 7. Dockerfile for Containerization

```dockerfile
FROM golang:1.22-alpine
WORKDIR /app
COPY . .
RUN go build -o main .
EXPOSE 8080
CMD ["./main"]
```

Build the Docker image with `docker build -t ginapp .` and run it with `docker run -p 8080:8080 ginapp`.


### 8. Deploying to Cloud

Build and push your Docker image to a registry, then deploy on services like AWS ECS, Google Cloud Run, or Azure Container Apps. 
Set environment variables for secrets, database URLs, etc.

***

## Summary Table

| Aspect         | Example | Explanation              |
|----------------|---------|--------------------------|
| Routing        | `r.GET("/ping", ...)`1.      | Basic endpoint |
| Middleware     | `router.Use(LoggerMiddleware())`3.  | Global logic before/after requests |
| Binding        | `ShouldBindJSON`1. 7.   | Automated request parsing |
| GORM           | `db.AutoMigrate(&User{})`6. | ORM usage for DB models |
| JWT Auth       | `AuthMiddleware()`9. 10.  | Protected endpoints |
| Docker         | `Dockerfile`6. 8.       | Deploy as a container |

Gin makes it straightforward to build feature-rich, secure, and scalable APIs in Go, with straightforward extension to cloud-native deployments1. 6. 7. .

### Sources
1.  gin-gonic/gin: Gin is a HTTP web framework written in Go ... - GitHub https://github.com/gin-gonic/gin
2.  Gin Web Framework https://gin-gonic.com
3.  Applying Design Patterns with Gin and Golang - Squash.io https://www.squash.io/applying-design-patterns-in-golang-with-gin-framework/
4.  Go with the Gin Framework - Earthly Blog https://earthly.dev/blog/golang-gin-framework/
5.  Best Practices of Building Web Apps with Gin & Golang - Squash.io https://www.squash.io/optimizing-gin-in-golang-project-structuring-error-handling-and-testing/
6.  RESTful API using GORM, Gin, and PostgreSQL in Go - techwasti https://techwasti.com/building-a-rest-api-with-gorm-gin-framework-and-postgresql-in-go
7.  Building a REST API with Golang using Gin and Gorm https://blog.logrocket.com/rest-api-golang-gin-gorm/
8.  Go REST API With GIN & GORM | Postgres | Docker - YouTube https://www.youtube.com/watch?v=ZI6HaPKHYsg
9.  Build a RESTful API using Golang and Gin https://www.twilio.com/en-us/blog/developers/community/build-restful-api-using-golang-and-gin
10.  JWT Authentication in Go with Gin and Middleware https://saadkhaleeq.com/jwt-authentication-in-go-with-gin-and-middleware
11.  Using Golang and Gin Framework to Build RESTful API https://dev.to/nikl/using-goland-and-gin-framework-to-build-restful-api-7-steps-2834
12.  Golang Integration Test With Gin, Gorm, Testify, PostgreSQL https://dev.to/truongpx396/golang-integration-test-with-gin-gorm-testify-postgresql-1e8m
13.  GOLANG INTEGRATION TEST WITH GIN, GORM, TESTIFY, MYSQL https://dev.to/truongpx396/golang-integration-test-with-gin-gorm-testify-mysql-20na
14.  Tutorial: Developing a RESTful API with Go and Gin https://go.dev/doc/tutorial/web-service-gin
15.  Build a Secure REST API with Go, Gin & JWT Authentication https://www.youtube.com/watch?v=qt917DG91VI
16.  Build Simple Authentication with Golang + Gin https://blog.mahad.dev/its-simple-to-build-your-own-emailpassword-authentication-rest-api
17.  A Deep Dive into Gin: Golang's Leading Framework - DEV Community https://dev.to/leapcell/a-deep-dive-into-gin-golangs-leading-framework-5e39
18.  Building a Robust Gin Example Project: A Guide for Go ... https://withcodeexample.com/building-a-robust-gin-example-project-a-guide-for-go-developers/
19.  Guide to making a To-Do Application in Go Programming using Gin ... https://golang.company/blog/guide-to-making-a-to-do-application-in-go-programming-using-gin-framework
20.  JWT Authentication in Golang using Gin Web Framework- ... https://golang.company/blog/jwt-authentication-in-golang-using-gin-web-framework

-----

# Role-based authorization in **Gin** applications
is achieved by verifying user roles (often stored in JWT claims or extracted from headers) inside middleware, and restricting access to endpoints based on those roles. 
This pattern is also known as Role-Based Access Control (RBAC).

## Core Approaches

### Simple Middleware for Roles
A basic approach creates custom middleware that checks a user’s role, e.g., from request headers or context:

```go
func roleMiddleware(role string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userRole := c.GetHeader("Role")
        if userRole != role {
            c.JSON(http.StatusForbidden, gin.H{"error": "Access forbidden"})
            c.Abort()
            return
        }
        c.Next()
    }
}

admin := r.Group("/admin", roleMiddleware("admin"))
admin.GET("/dashboard", adminDashboard)
```

This restricts specific routes to roles like `"admin"`, but can be extended for multiple roles, claims, or extracted from JWTs1. .

### Role Checks Using JWT Claims
With JWT-based authentication, roles are usually claims in the token. 
A JWT is parsed at the start (in authentication middleware) and the role stored on Gin’s context. 
A typical pattern:

```go
func RoleAuthorize(allowedRoles ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Example: extract user from context (put there by JWT middleware)
        user, exists := c.Get("user")
        if !exists { /* Unauthorized logic */ }
        claims := user.(*jwt.StandardClaims)
        hasRole := false
        for _, r := range allowedRoles {
            if claims.Role == r { hasRole = true; break }
        }
        if !hasRole {
            c.JSON(403, gin.H{"error": "Forbidden"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```
Apply this with `Route.Group("/admin").Use(RoleAuthorize("admin"))` and similar patterns2. 3. 4. .

### RBAC with Libraries (e.g., Casbin, gin-rbac)

For enterprise RBAC, frameworks like **Casbin** or **gin-rbac** let you use a policy-based approach, decoupling permissions from routes:

```go
import (
    "github.com/casbin/casbin"
    "github.com/gin-gonic/gin"
)
e := casbin.NewEnforcer("examples/rbac_model.conf", "examples/rbac_policy.csv")

r.Use(func(c *gin.Context) {
    userRole := getCurrentUserRole(c)
    if ok, _ := e.Enforce(userRole, c.Request.URL.Path, c.Request.Method); !ok {
        c.JSON(403, gin.H{"error": "Access denied"})
        c.Abort()
        return
    }
    c.Next()
})
```
This approach scales for complex, multi-role or permissions scenarios, and supports external policy management5. 6. .

## Example: Protecting Endpoints

```go
// Middleware example
router.GET("/profile", RoleAuthorize("user", "admin"))
router.DELETE("/user/:id", RoleAuthorize("admin"))
```
Users with one of the allowed roles succeed, others get a forbidden error1. 2. .

## Advanced: Multi-Tenant & Organization Permissions

For SaaS and multi-tenant APIs, combine role validation with organization context from JWT claims, applying middleware accordingly for global, organization, or resource-level access3. .

***

Role-based authorization in Gin is best handled with middleware that inspects user roles (from headers or JWT claims) and conditionally allows or blocks requests; for complex systems, dedicated RBAC libraries like Casbin streamline and harden the implementation1. 5. 2. 3. .

### Sources
1.  Authentication And Authorization In Gin - WCE Academy https://academy.withcodeexample.com/gin-for-beginners-build-apis-with-golang-easily/authentication-and-authorization-in-gin
2.  Implementing JWT Authentication In Go - Permify https://permify.co/post/jwt-authentication-go/
3.  Protect your Gin API with RBAC and JWT validation - Logto docs https://docs.logto.io/api-protection/go/gin
4.  Role-based Access Control in Golang with jwt-go - DEV Community https://dev.to/bensonmacharia/role-based-access-control-in-golang-with-jwt-go-ijn
5.  Enterprise Functionalities in Golang: SSO, RBAC and Audit Trails in ... https://www.squash.io/enterprise-functionalities-in-golang-sso-rbac-and-audit-trails-in-gin/
6.  Role-based access control (RBAC) middleware for Gin web framework https://github.com/aiyi/gin-rbac
7.  RBAC with Gin and possible casbin : r/golang - Reddit https://www.reddit.com/r/golang/comments/noxezs/rbac_with_gin_and_possible_casbin/
8.  Gin Web Framework https://gin-gonic.com
9.  appleboy/gin-jwt: JWT Middleware for Gin framework - GitHub https://github.com/appleboy/gin-jwt
10.  Implementing Complete Permission Authentication in Go Applications https://goframe.org/en/articles/rbac-permission-authentication-in-goframe
11.  JWT in Action: Secure Authentication & Authorization in Go https://dev.to/leapcell/jwt-in-action-secure-authentication-authorization-in-go-jde
12.  Middleware in Gin | Golang - GeeksforGeeks https://www.geeksforgeeks.org/go-language/middleware-in-gin-golang/
13.  gin JWT - Rob Reid https://robreid.io/gin-jwt/
14.  Creating An Role Based Authentication Server using GoLang And ... https://blog.devops.dev/creating-an-role-based-authentication-server-using-golang-and-fiber-3367341cf7c5
15.  Implementing ROLE with Gin-JWT - Stack Overflow https://stackoverflow.com/questions/55305183/implementing-role-with-gin-jwt
16.  How to Implement Authentication and Authorization in Golang. https://tanmay-vaish.hashnode.dev/how-to-implement-authentication-and-authorization-in-golang

-----

# CRUD API for an **Article** entity
in a Gin application with GORM, define an Article model and handlers for create, read, update, and delete operations. 
The following is a standard implementation pattern.

## Article Model

```go
type Article struct {
    ID        uint   `gorm:"primaryKey" json:"id"`
    Title     string `json:"title"`
    Content   string `json:"content"`
    Author    string `json:"author"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```
After defining the model, auto-migrate it with `db.AutoMigrate(&Article{})`1. 2. .

## CRUD Handlers

### Create Article

```go
func CreateArticle(c *gin.Context) {
    var article Article
    if err := c.ShouldBindJSON(&article); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    db.Create(&article)
    c.JSON(201, article)
}
```

### Read All Articles

```go
func GetArticles(c *gin.Context) {
    var articles []Article
    db.Find(&articles)
    c.JSON(200, articles)
}
```

### Read Article by ID

```go
func GetArticleByID(c *gin.Context) {
    var article Article
    id := c.Param("id")
    if err := db.First(&article, id).Error; err != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    c.JSON(200, article)
}
```

### Update Article

```go
func UpdateArticle(c *gin.Context) {
    var article Article
    id := c.Param("id")
    if err := db.First(&article, id).Error; err != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    if err := c.ShouldBindJSON(&article); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    db.Save(&article)
    c.JSON(200, article)
}
```

### Delete Article

```go
func DeleteArticle(c *gin.Context) {
    id := c.Param("id")
    if err := db.Delete(&Article{}, id).Error; err != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    c.JSON(204, nil)
}
```


## Registering the Article Routes

```go
r := gin.Default()
r.POST("/articles", CreateArticle)
r.GET("/articles", GetArticles)
r.GET("/articles/:id", GetArticleByID)
r.PUT("/articles/:id", UpdateArticle)
r.DELETE("/articles/:id", DeleteArticle)
```

This structure provides all the endpoints required for CRUD operations on an Article resource using Gin and GORM1. 3. 2. .

***

This approach ensures efficient and idiomatic CRUD operations following REST principles in Go with Gin and GORM1. 4. 2. .

### Sources
1.  RESTful API using GORM, Gin, and PostgreSQL in Go - techwasti https://techwasti.com/building-a-rest-api-with-gorm-gin-framework-and-postgresql-in-go
2.  Golang CRUD REST API with Gin and Gorm (Service ... https://blog.stackademic.com/golang-crud-rest-api-with-gin-and-gorm-service-repository-pattern-167afa8e9e87
3.  CRUD api with Go Gin framework (production ready) https://dev.to/devniklesh/crud-api-with-go-gin-framework-production-ready-52jd
4.  CRUD operations using GORM go lang mysql - techwasti https://techwasti.com/mastering-basic-crud-operations-with-gorm-in-go-a-comprehensive-guide
5.  Basic CRUD Operations Using Golang, Gin Gonic, and ... https://dev.to/awahids/basic-crud-operations-using-golang-gin-gonic-and-gorm-53
6.  A CRUD API with Go, using the Gin framework and MongoDB. https://dev.to/negin/a-crud-api-with-go-using-the-gin-framework-and-mongodb-379e
7.  khachatryanhovhannes/crud_golanng: A simple CRUD ... - GitHub https://github.com/khachatryanhovhannes/crud_golanng
8.  Go REST Guide. Gin Framework - JetBrains https://www.jetbrains.com/guide/go/tutorials/rest_api_series/gin/
9.  How to Build a CRUD App with Golang, Gin, and PostgreSQL https://dev.to/mazyaryousefinia/how-to-build-a-crud-app-with-golang-gin-and-postgresql-4h4l
10.  Creating a JSON CRUD API in Go (Gin/GORM) https://www.youtube.com/watch?v=lf_kiH_NPvM
11.  How to build a CRUD REST API with Go, Gin and Fauna https://dev.to/faruq2/how-to-build-a-crud-rest-api-with-go-gin-and-fauna-37o6
12.  Go: CRUD API using Gin Framework - DEV Community https://dev.to/ankitmalikg/go-crud-api-using-gin-framework-36h9
13.  GoLang, GORM & Gin CRUD Example https://github.com/herusdianto/gorm_crud_example
14.  How to structure an API in Go for a crud application : r/golang - Reddit https://www.reddit.com/r/golang/comments/1bqrhn8/how_to_structure_an_api_in_go_for_a_crud/
15.  Golang CRUD Operation with MongoDB | by Saurav Maharjan https://articles.readytowork.jp/golang-crud-operation-with-mongodb-0fd03b89b103
16.  A step-by-step guide to creating production-ready APIs in Go with ... https://www.honeybadger.io/blog/a-step-by-step-guide-to-creating-production-ready-apis-in-go-with-gin-and-gorm/
17.  Building a RESTful API in Go with Gin and PostgreSQL - Go Learn https://opyjo.hashnode.dev/building-a-restful-api-in-go-with-gin-and-postgresql
18.  Beginner Go/Gin CRUD API - Seeking Code Review and ... https://www.reddit.com/r/golang/comments/1jmmnet/beginner_gogin_crud_api_seeking_code_review_and/
19.  Tutorial: Developing a RESTful API with Go and Gin https://go.dev/doc/tutorial/web-service-gin
20.  cgrant/gin-gorm-api-example: [Article] Minimal code for ... https://github.com/cgrant/gin-gorm-api-example

To ensure only the **author** (the user who created an article) can update or delete it in Gin, store the `AuthorID` as a foreign key referencing the User. Enforce ownership checks in update/delete handlers using the authenticated user from context. On create, set the `AuthorID` from the logged-in user's ID.

## Model Adjustment

```go
type Article struct {
    ID        uint      `gorm:"primaryKey" json:"id"`
    Title     string    `json:"title"`
    Content   string    `json:"content"`
    AuthorID  uint      `json:"author_id"`
    Author    User      `gorm:"foreignKey:AuthorID" json:"author"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

type User struct {
    ID       uint   `gorm:"primaryKey"`
    Username string
    // ... other fields
}
```


## Create Article: Set Author

Assuming the JWT middleware (or equivalent) puts the authenticated User in the Gin context:

```go
func CreateArticle(c *gin.Context) {
    var article Article
    if err := c.ShouldBindJSON(&article); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    userVal, exists := c.Get("user")
    if !exists {
        c.JSON(401, gin.H{"error": "Unauthorized"})
        return
    }
    user := userVal.(User)
    article.AuthorID = user.ID    // Set the author as the logged-in user
    db.Create(&article)
    c.JSON(201, article)
}
```


## Update/Delete Article: Ownership Check

Update and delete handlers verify the logged-in user owns the article:

```go
func UpdateArticle(c *gin.Context) {
    id := c.Param("id")
    var article Article
    if db.First(&article, id).Error != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    userVal, exists := c.Get("user")
    if !exists || article.AuthorID != userVal.(User).ID {
        c.JSON(403, gin.H{"error": "Forbidden"})
        return
    }
    if err := c.ShouldBindJSON(&article); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    db.Save(&article)
    c.JSON(200, article)
}

func DeleteArticle(c *gin.Context) {
    id := c.Param("id")
    var article Article
    if db.First(&article, id).Error != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    userVal, exists := c.Get("user")
    if !exists || article.AuthorID != userVal.(User).ID {
        c.JSON(403, gin.H{"error": "Forbidden"})
        return
    }
    db.Delete(&article)
    c.JSON(204, nil)
}
```


***

This pattern ensures article authorship on creation and restricts modification/removal to the owning user, enforcing resource-level authorization in a Gin API using context-injected authentication and GORM foreign keys1. 5. 3. .

### Sources
1.  Golang RESTful API with Gin, Gorm, PostgreSQL https://dev.to/truongpx396/golang-restful-api-with-gin-gorm-postgresql-2hc
2.  How to Build a REST API with Golang using Gin and Gorm https://rahmanfadhil.com/golang-rest-api/
3.  Building a REST API with Golang using Gin and Gorm https://blog.logrocket.com/rest-api-golang-gin-gorm/
4.  A step-by-step guide to creating production-ready APIs in Go ... https://www.honeybadger.io/blog/a-step-by-step-guide-to-creating-production-ready-apis-in-go-with-gin-and-gorm/
5.  Golang Authentication with Gin and Gorm https://blog.stackademic.com/golang-authentication-with-gin-and-gorm-c87d7cba1ed3
6.  Writing a Fully Fledged REST API Using PostgreSQL, Gin, ... https://betterprogramming.pub/writing-a-fully-fledged-api-using-postgresql-gin-and-gorm-4d5ba73114da
7.  Golang (GIN Framework) : GORM Query didnt work https://stackoverflow.com/questions/45689020/golang-gin-framework-gorm-query-didnt-work
8.  CRUD api with Go Gin framework (production ready) https://dev.to/devniklesh/crud-api-with-go-gin-framework-production-ready-52jd

----

To strictly enforce an "Author-only" policy for article update and delete in Gin, always verify in your handler that the authenticated user's ID matches the article's `AuthorID`. Deny all such requests where this ownership does not match.

## Example: Enforcing Author-Only Policy

Below is the standard and effective pattern for protecting update/delete routes:

```go
func UpdateArticle(c *gin.Context) {
    id := c.Param("id")
    var article Article
    if db.First(&article, id).Error != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    userVal, exists := c.Get("user")
    if !exists {
        c.JSON(401, gin.H{"error": "Unauthorized"})
        return
    }
    user := userVal.(User)
    if article.AuthorID != user.ID {
        c.JSON(403, gin.H{"error": "Forbidden: you are not the author"})
        return
    }
    if err := c.ShouldBindJSON(&article); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    db.Save(&article)
    c.JSON(200, article)
}

func DeleteArticle(c *gin.Context) {
    id := c.Param("id")
    var article Article
    if db.First(&article, id).Error != nil {
        c.JSON(404, gin.H{"error": "Article not found"})
        return
    }
    userVal, exists := c.Get("user")
    if !exists {
        c.JSON(401, gin.H{"error": "Unauthorized"})
        return
    }
    user := userVal.(User)
    if article.AuthorID != user.ID {
        c.JSON(403, gin.H{"error": "Forbidden: you are not the author"})
        return
    }
    db.Delete(&article)
    c.JSON(204, nil)
}
```


## How it works

- Fetch the article by ID.
- Retrieve the logged-in user from context.
- Compare the `article.AuthorID` to the authenticated user's ID.
- Allow operation only if they match; else return a **403 Forbidden** error.

This policy blocks all update/delete operations for anyone who is not the resource owner, fully enforcing author-level access control for sensitive operations in a Gin API10. 11. 12. .

Sources
1.  Enabling data policy enforcement on your ... https://learn.microsoft.com/en-us/purview/legacy/how-to-enable-data-policy-enforcement
2.  8 Implementing Policy Enforcement Options and Labeling ... https://docs.oracle.com/database/121/OLSAG/enforce.htm
3.  Update a policy https://docs.cyberark.com/conjur-enterprise/latest/en/content/developer/conjur_api_update_policy.htm
4.  Create, list, update, and delete Microsoft Purview DevOps ... https://learn.microsoft.com/en-us/purview/legacy/how-to-policies-devops-authoring-generic
5.  how can i delete/update author name if book ... https://stackoverflow.com/questions/64450237/how-can-i-delete-update-author-name-if-book-name-is-updated-or-deleted-using-res
6.  Is there any way to prevent everyone to delete or modify a ... https://github.com/Azure/azure-policy/issues/389
7.  Merge request approval policies https://docs.gitlab.com/user/application_security/policies/merge_request_approval_policies/
8.  Enforce Where clause on every update statement https://www.sqlservercentral.com/forums/topic/enforce-where-clause-on-every-update-statement
9.  Chapter 6. Managing security policies | Operating https://docs.redhat.com/en/documentation/red_hat_advanced_cluster_security_for_kubernetes/4.6/html/operating/managing-security-policies
10.  Golang RESTful API with Gin, Gorm, PostgreSQL https://dev.to/truongpx396/golang-restful-api-with-gin-gorm-postgresql-2hc
11.  Golang Authentication with Gin and Gorm https://blog.stackademic.com/golang-authentication-with-gin-and-gorm-c87d7cba1ed3
12.  Building a REST API with Golang using Gin and Gorm https://blog.logrocket.com/rest-api-golang-gin-gorm/
