# Concurrency in Go: Patterns and Flow Control

Go's concurrency model is one of its most powerful features, built around the philosophy of "Don't communicate by sharing memory; share memory by communicating." Let me explain the key concepts, patterns, and flow control mechanisms.

## Core Concurrency Primitives

### 1. Goroutines
Goroutines are lightweight threads managed by the Go runtime. They're much cheaper than OS threads:

```go
// Starting a goroutine
go func() {
    fmt.Println("Running in a goroutine")
}()

// Or with a named function
go myFunction()
```

### 2. Channels
Channels are the primary way goroutines communicate:

```go
// Unbuffered channel
ch := make(chan int)

// Buffered channel
buffered := make(chan int, 10)

// Send and receive
ch <- 42        // Send
value := <-ch   // Receive
```

### 3. Select Statement
The `select` statement allows you to wait on multiple channel operations:

```go
select {
case msg1 := <-ch1:
    fmt.Println("Received from ch1:", msg1)
case msg2 := <-ch2:
    fmt.Println("Received from ch2:", msg2)
case <-time.After(1 * time.Second):
    fmt.Println("Timeout!")
default:
    fmt.Println("No channels ready")
}
```

## Common Concurrency Patterns

### 1. For-Select-Done Pattern
This pattern uses an infinite loop with select to handle events and graceful shutdown:

```go
func worker(ctx context.Context) {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            // Do periodic work
            doWork()
        case <-ctx.Done():
            fmt.Println("Worker stopping")
            return
        }
    }
}
```

### 2. Pipeline Pattern
Pipelines process data through multiple stages connected by channels:

```go
// Generator stage
func generate(nums ...int) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for _, n := range nums {
            out <- n
        }
    }()
    return out
}

// Processing stage
func square(in <-chan int) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for n := range in {
            out <- n * n
        }
    }()
    return out
}

// Usage
numbers := generate(1, 2, 3, 4)
squared := square(numbers)
for result := range squared {
    fmt.Println(result)
}
```

### 3. Fan-Out/Fan-In Pattern
**Fan-out**: Distribute work across multiple goroutines
**Fan-in**: Combine results from multiple goroutines

```go
// Fan-out: multiple workers reading from same channel
func fanOut(in <-chan int, workers int) []<-chan int {
    channels := make([]<-chan int, workers)
    for i := 0; i < workers; i++ {
        out := make(chan int)
        channels[i] = out
        go func() {
            defer close(out)
            for n := range in {
                out <- process(n)
            }
        }()
    }
    return channels
}

// Fan-in: merge multiple channels into one
func fanIn(channels ...<-chan int) <-chan int {
    var wg sync.WaitGroup
    out := make(chan int)
    
    for _, ch := range channels {
        wg.Add(1)
        go func(c <-chan int) {
            defer wg.Done()
            for n := range c {
                out <- n
            }
        }(ch)
    }
    
    go func() {
        wg.Wait()
        close(out)
    }()
    
    return out
}
```

### 4. Worker Pool Pattern
Limit the number of concurrent workers processing tasks:

```go
func workerPool(jobs <-chan Job, results chan<- Result, numWorkers int) {
    var wg sync.WaitGroup
    
    // Start workers
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for job := range jobs {
                result := processJob(job)
                results <- result
            }
        }()
    }
    
    // Close results when all workers are done
    go func() {
        wg.Wait()
        close(results)
    }()
}
```

### 5. Errgroup Pattern
Handle errors from multiple goroutines using `golang.org/x/sync/errgroup`:

```go
import "golang.org/x/sync/errgroup"

func processWithErrgroup() error {
    g := new(errgroup.Group)
    
    // Start multiple tasks
    g.Go(func() error {
        return task1()
    })
    
    g.Go(func() error {
        return task2()
    })
    
    g.Go(func() error {
        return task3()
    })
    
    // Wait for all to complete, return first error
    return g.Wait()
}
```

## Flow Control Mechanisms

### 1. Context for Cancellation
Use `context.Context` for cancellation and timeouts:

```go
func doWorkWithTimeout(ctx context.Context) error {
    // Create a timeout context
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    select {
    case result := <-doLongRunningWork():
        return processResult(result)
    case <-ctx.Done():
        return ctx.Err() // Returns timeout or cancellation error
    }
}
```

### 2. Sync Package Primitives

**WaitGroup**: Wait for multiple goroutines to complete
```go
var wg sync.WaitGroup

for i := 0; i < 10; i++ {
    wg.Add(1)
    go func(id int) {
        defer wg.Done()
        doWork(id)
    }(i)
}

wg.Wait() // Wait for all goroutines to finish
```

**Mutex**: Protect shared resources
```go
var (
    mu      sync.Mutex
    counter int
)

func increment() {
    mu.Lock()
    defer mu.Unlock()
    counter++
}
```

**Once**: Ensure something happens only once
```go
var once sync.Once

func initialize() {
    once.Do(func() {
        // This will only run once, even if called multiple times
        setupResources()
    })
}
```

### 3. Channel-Based Flow Control

**Buffered channels** for rate limiting:
```go
// Limit to 10 concurrent operations
semaphore := make(chan struct{}, 10)

func limitedOperation() {
    semaphore <- struct{}{} // Acquire
    defer func() { <-semaphore }() // Release
    
    // Do work
}
```

**Closing channels** for broadcast signaling:
```go
done := make(chan struct{})

// Signal all goroutines to stop
close(done)

// In goroutines:
select {
case <-done:
    return // Stop working
default:
    // Continue working
}
```

## Best Practices

1. **Always handle goroutine lifecycle**: Ensure goroutines can exit cleanly
2. **Use contexts for cancellation**: Pass `context.Context` to long-running operations
3. **Avoid goroutine leaks**: Make sure all goroutines have a way to terminate
4. **Channel ownership**: The sender should close the channel, not the receiver
5. **Use buffered channels carefully**: They can hide synchronization issues
6. **Prefer channels over shared memory**: Follow Go's concurrency philosophy

## Advanced Patterns

### Rate Limiting with Ticker
```go
func rateLimitedWorker(ctx context.Context, rate time.Duration) {
    ticker := time.NewTicker(rate)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            doWork()
        case <-ctx.Done():
            return
        }
    }
}
```

### Circuit Breaker Pattern
```go
type CircuitBreaker struct {
    failures    int
    maxFailures int
    timeout     time.Duration
    lastFailure time.Time
    mu          sync.Mutex
}

func (cb *CircuitBreaker) Call(fn func() error) error {
    cb.mu.Lock()
    defer cb.mu.Unlock()
    
    if cb.failures >= cb.maxFailures {
        if time.Since(cb.lastFailure) < cb.timeout {
            return errors.New("circuit breaker open")
        }
        cb.failures = 0 // Reset after timeout
    }
    
    err := fn()
    if err != nil {
        cb.failures++
        cb.lastFailure = time.Now()
    }
    
    return err
}
```

Go's concurrency model provides powerful tools for building concurrent applications. The key is understanding when to use each pattern and how to compose them effectively for your specific use case.

-------

# Go Context: Deep Dive into Concurrency Control

The `context` package in Go is a fundamental tool for managing the lifecycle, cancellation, and data propagation across goroutines. 
It's essential for building robust concurrent applications. Let me explain the key concepts and patterns in detail.

## What is Context?

Context provides a way to carry deadlines, cancellation signals, and request-scoped values across API boundaries and between goroutines. 
It follows the philosophy of "explicit is better than implicit" by making cancellation and timeouts explicit parts of your API.

## Core Context Interface

```go
type Context interface {
    Deadline() (deadline time.Time, ok bool)
    Done() <-chan struct{}
    Err() error
    Value(key interface{}) interface{}
}
```

## Creating Contexts

### 1. Background and TODO
```go
// Root context - never canceled, no values, no deadline
ctx := context.Background()

// Use when unsure which context to use
ctx := context.TODO()
```

### 2. WithCancel - Manual Cancellation
```go
ctx, cancel := context.WithCancel(context.Background())
defer cancel() // Always call cancel to release resources

go func() {
    select {
    case <-ctx.Done():
        fmt.Println("Operation canceled:", ctx.Err())
        return
    case <-time.After(5 * time.Second):
        fmt.Println("Work completed")
    }
}()

// Cancel after 2 seconds
time.Sleep(2 * time.Second)
cancel()
```

### 3. WithTimeout - Automatic Cancellation
```go
// Cancel automatically after 3 seconds
ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
defer cancel()

result, err := doWork(ctx)
if err != nil {
    if errors.Is(err, context.DeadlineExceeded) {
        fmt.Println("Operation timed out")
    }
}
```

### 4. WithDeadline - Cancel at Specific Time
```go
deadline := time.Now().Add(5 * time.Minute)

ctx, cancel := context.WithDeadline(context.Background(), deadline)
defer cancel()
```

### 5. WithValue - Carrying Data
```go
type userKey string

ctx := context.WithValue(context.Background(), userKey("userID"), 12345)

// Retrieve value
if userID, ok := ctx.Value(userKey("userID")).(int); ok {
    fmt.Println("User ID:", userID)
}
```

## Advanced Context Features (Go 1.20+)

### WithCancelCause - Custom Cancellation Reasons
```go
ctx, cancel := context.WithCancelCause(context.Background())

go func() {
    // Cancel with a specific reason
    cancel(errors.New("database connection failed"))
}()

<-ctx.Done()
fmt.Println("Cancellation cause:", context.Cause(ctx))
```

### WithTimeoutCause and WithDeadlineCause
```go
ctx, cancel := context.WithTimeoutCause(
    context.Background(), 
    2*time.Second, 
    errors.New("API request timeout")
)
defer cancel()
```

## Context Patterns and Best Practices

### 1. HTTP Request Context
```go
func handleRequest(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context() // Get request context
    
    // Add timeout for downstream operations
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    data, err := fetchDataFromAPI(ctx)
    if err != nil {
        if errors.Is(err, context.DeadlineExceeded) {
            http.Error(w, "Request timeout", http.StatusRequestTimeout)
            return
        }
        http.Error(w, "Internal error", http.StatusInternalServerError)
        return
    }
    
    json.NewEncoder(w).Encode(data)
}

func fetchDataFromAPI(ctx context.Context) ([]byte, error) {
    req, err := http.NewRequestWithContext(ctx, "GET", "https://api.example.com/data", nil)
    if err != nil {
        return nil, err
    }
    
    resp, err := http.DefaultClient.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    return io.ReadAll(resp.Body)
}
```

### 2. Database Operations with Context
```go
func getUserByID(ctx context.Context, db *sql.DB, userID int) (*User, error) {
    query := "SELECT id, name, email FROM users WHERE id = $1"
    
    row := db.QueryRowContext(ctx, query, userID)
    
    var user User
    err := row.Scan(&user.ID, &user.Name, &user.Email)
    if err != nil {
        return nil, err
    }
    
    return &user, nil
}
```

### 3. Worker Pool with Context
```go
func workerPool(ctx context.Context, jobs <-chan Job, results chan<- Result, numWorkers int) {
    var wg sync.WaitGroup
    
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for {
                select {
                case job, ok := <-jobs:
                    if !ok {
                        return // Channel closed
                    }
                    
                    result := processJob(ctx, job)
                    select {
                    case results <- result:
                    case <-ctx.Done():
                        return // Context canceled
                    }
                    
                case <-ctx.Done():
                    return // Context canceled
                }
            }
        }()
    }
    
    go func() {
        wg.Wait()
        close(results)
    }()
}
```

### 4. Graceful Shutdown Pattern
```go
func main() {
    ctx, cancel := context.WithCancel(context.Background())
    
    // Handle shutdown signals
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
    
    go func() {
        <-sigChan
        fmt.Println("Shutdown signal received")
        cancel() // Cancel context to stop all operations
    }()
    
    // Start services
    var wg sync.WaitGroup
    
    wg.Add(1)
    go func() {
        defer wg.Done()
        runWebServer(ctx)
    }()
    
    wg.Add(1)
    go func() {
        defer wg.Done()
        runBackgroundWorker(ctx)
    }()
    
    wg.Wait()
    fmt.Println("All services stopped")
}

func runWebServer(ctx context.Context) {
    server := &http.Server{Addr: ":8080"}
    
    go func() {
        <-ctx.Done()
        shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        server.Shutdown(shutdownCtx)
    }()
    
    server.ListenAndServe()
}
```

### 5. Pipeline with Context
```go
func pipeline(ctx context.Context, input <-chan int) <-chan int {
    output := make(chan int)
    
    go func() {
        defer close(output)
        for {
            select {
            case value, ok := <-input:
                if !ok {
                    return
                }
                
                // Process value
                processed := value * 2
                
                select {
                case output <- processed:
                case <-ctx.Done():
                    return
                }
                
            case <-ctx.Done():
                return
            }
        }
    }()
    
    return output
}
```

## Context Best Practices

### 1. Function Signatures
```go
// Context should be the first parameter
func DoSomething(ctx context.Context, arg1 string, arg2 int) error {
    // Implementation
}

// Don't store context in structs
type Service struct {
    // ctx context.Context // DON'T do this
    db *sql.DB
}

// Pass context to methods instead
func (s *Service) ProcessData(ctx context.Context, data []byte) error {
    // Implementation
}
```

### 2. Value Keys
```go
// Define unexported key types to avoid collisions
type contextKey string

const (
    userIDKey    contextKey = "userID"
    requestIDKey contextKey = "requestID"
)

// Provide type-safe accessors
func WithUserID(ctx context.Context, userID int) context.Context {
    return context.WithValue(ctx, userIDKey, userID)
}

func UserIDFromContext(ctx context.Context) (int, bool) {
    userID, ok := ctx.Value(userIDKey).(int)
    return userID, ok
}
```

### 3. Error Handling
```go
func handleContextError(err error) {
    switch {
    case errors.Is(err, context.Canceled):
        fmt.Println("Operation was canceled")
    case errors.Is(err, context.DeadlineExceeded):
        fmt.Println("Operation timed out")
    default:
        fmt.Println("Other error:", err)
    }
}
```

### 4. Testing with Context
```go
func TestWithTimeout(t *testing.T) {
    ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
    defer cancel()
    
    err := slowOperation(ctx)
    if !errors.Is(err, context.DeadlineExceeded) {
        t.Errorf("Expected timeout error, got: %v", err)
    }
}

func TestWithCancel(t *testing.T) {
    ctx, cancel := context.WithCancel(context.Background())
    
    go func() {
        time.Sleep(50 * time.Millisecond)
        cancel()
    }()
    
    err := longRunningOperation(ctx)
    if !errors.Is(err, context.Canceled) {
        t.Errorf("Expected cancellation error, got: %v", err)
    }
}
```

## Common Anti-Patterns to Avoid

1. **Don't pass nil context** - Use `context.TODO()` if unsure
2. **Don't store context in structs** - Pass explicitly to methods
3. **Don't ignore context in loops** - Check `ctx.Done()` regularly
4. **Don't forget to call cancel()** - Always defer cancel functions
5. **Don't use context for optional parameters** - Use regular function parameters

## Real-World Example: API Client with Context

```go
type APIClient struct {
    baseURL string
    client  *http.Client
}

func (c *APIClient) GetUser(ctx context.Context, userID int) (*User, error) {
    url := fmt.Sprintf("%s/users/%d", c.baseURL, userID)
    
    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return nil, fmt.Errorf("creating request: %w", err)
    }
    
    resp, err := c.client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("making request: %w", err)
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API error: %d", resp.StatusCode)
    }
    
    var user User
    if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
        return nil, fmt.Errorf("decoding response: %w", err)
    }
    
    return &user, nil
}

// Usage with timeout
func main() {
    client := &APIClient{
        baseURL: "https://api.example.com",
        client:  &http.Client{},
    }
    
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    user, err := client.GetUser(ctx, 123)
    if err != nil {
        if errors.Is(err, context.DeadlineExceeded) {
            fmt.Println("Request timed out")
        } else {
            fmt.Printf("Error: %v\n", err)
        }
        return
    }
    
    fmt.Printf("User: %+v\n", user)
}
```

Context is essential for building robust, cancellable, and timeout-aware concurrent applications in Go. It provides a clean way to manage the lifecycle of operations and propagate important information across goroutine boundaries.
