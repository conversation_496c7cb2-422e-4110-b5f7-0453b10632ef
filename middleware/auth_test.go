package middleware

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

// Example of how to use the middleware with different parameters
func ExampleAuthorizeOwner() {
	router := gin.New()

	// For events where we check the "Author" field against "user" context key
	router.PUT("/events/:id", <PERSON><PERSON><PERSON><PERSON><PERSON>("Author", "user"), func(c *gin.Context) {
		// Handler logic here
		c.<PERSON>(http.StatusOK, gin.H{"message": "Event updated"})
	})

	// For a different resource type where we might check "Owner" field against "username" context key
	router.PUT("/posts/:id", AuthorizeOwner("Owner", "username"), func(c *gin.Context) {
		// Handler logic here
		c.<PERSON>(http.StatusOK, gin.H{"message": "Post updated"})
	})

	// For documents with "CreatedBy" field against "userId" context key
	router.DELETE("/documents/:id", AuthorizeOwner("Created<PERSON><PERSON>", "userId"), func(c *gin.Context) {
		// Handler logic here
		c.<PERSON>(http.StatusNoContent, nil)
	})
}
