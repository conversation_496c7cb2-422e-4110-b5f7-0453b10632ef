package middleware

import (
	"events-api/database"
	"events-api/model"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"reflect"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenString := c.<PERSON>("Authorization")
		if tokenString == "" {
			c.J<PERSON>(401, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		user := validateToken(tokenString)

		if user.ID == 0 {
			c.JSON(401, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		c.Set("user", user.Username)

		log.Println("Username: ", user.Username)

		c.Next()
	}
}

// AuthorizeOwner creates a middleware that checks if the authenticated user is the owner/author of a resource
// Parameters:
// - field: the field name in the resource to compare against (e.g., "Author")
// - contextKey: the key to get the user identifier from gin context (e.g., "user", "username")
func AuthorizeOwner(field string, contextKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the resource ID from URL parameter
		id := c.Param("id")
		if id == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Resource ID is required"})
			c.Abort()
			return
		}

		// Get the authenticated user from context
		userValue, exists := c.Get(contextKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		username, ok := userValue.(string)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user context"})
			c.Abort()
			return
		}

		// Find the event by ID
		var event model.Event
		if err := database.DB.First(&event, id).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Resource not found"})
			c.Abort()
			return
		}

		// Use reflection to get the field value from the event
		eventValue := reflect.ValueOf(event)
		fieldValue := eventValue.FieldByName(field)

		if !fieldValue.IsValid() {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid field specified"})
			c.Abort()
			return
		}

		// Convert field value to string for comparison
		fieldString := fieldValue.String()

		// Check if the user is the owner
		if fieldString != username {
			c.JSON(http.StatusForbidden, gin.H{"error": "Forbidden: You can only modify your own resources"})
			c.Abort()
			return
		}

		// Store the event in context for use in the handler
		c.Set("resource", event)
		c.Next()
	}
}

func validateToken(token string) *model.User {
	// JWT validation logic here
	user := model.User{}
	database.DB.First(&user, "username = ?", token)

	return &user
}
