# Authorization Middleware

This package contains middleware for handling authorization in the events API.

## AuthorizeOwner Middleware

The `AuthorizeOwner` middleware provides a flexible way to ensure that only the owner/author of a resource can perform update or delete operations on it.

### Usage

```go
import "events-api/middleware"

// Basic usage for events
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.UpdateEvent)
router.DELETE("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.DeleteEvent)
```

### Parameters

- **field** (string): The field name in the resource model to compare against (e.g., "Author", "Owner", "CreatedBy")
- **contextKey** (string): The key to retrieve the user identifier from the Gin context (e.g., "user", "username", "userId")

### How it works

1. **Authentication Check**: Verifies that the user is authenticated by checking if the specified context key exists
2. **Resource Lookup**: Fetches the resource by ID from the database
3. **Ownership Verification**: Uses reflection to compare the specified field value with the user identifier from context
4. **Context Storage**: If authorized, stores the resource in context as "resource" for use in the handler

### Examples

#### Different Field Names
```go
// For events with "Author" field
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.UpdateEvent)

// For posts with "Owner" field  
router.PUT("/posts/:id", middleware.AuthorizeOwner("Owner", "user"), route.UpdatePost)

// For documents with "CreatedBy" field
router.PUT("/documents/:id", middleware.AuthorizeOwner("CreatedBy", "user"), route.UpdateDocument)
```

#### Different Context Keys
```go
// Using "user" context key (set by AuthMiddleware)
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.UpdateEvent)

// Using "username" context key
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "username"), route.UpdateEvent)

// Using "userId" context key
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "userId"), route.UpdateEvent)
```

### Handler Implementation

When using this middleware, your handlers can retrieve the pre-validated resource from context:

```go
func UpdateEvent(c *gin.Context) {
    // Get the event from middleware context (already validated for ownership)
    eventInterface, exists := c.Get("resource")
    if !exists {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Resource not found in context"})
        return
    }

    event, ok := eventInterface.(model.Event)
    if !ok {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid resource type"})
        return
    }

    // Continue with your update logic...
}
```

### Error Responses

The middleware returns appropriate HTTP status codes:

- **400 Bad Request**: When resource ID is missing
- **401 Unauthorized**: When user is not authenticated
- **403 Forbidden**: When user is not the owner of the resource
- **404 Not Found**: When the resource doesn't exist
- **500 Internal Server Error**: When there are configuration issues

### Benefits

1. **Reusable**: Can be used with any resource type and field name
2. **Flexible**: Supports different context keys for user identification
3. **Secure**: Prevents unauthorized access to resources
4. **Clean**: Removes repetitive authorization code from handlers
5. **Testable**: Easy to unit test with different scenarios
